"use client"

import {
  IconChartBar,
  IconDashboard,
  IconFileAi,
  IconHelp,
  IconListDetails,
  IconReport,
  IconSearch,
  IconSettings,
  IconWallet,
} from "@tabler/icons-react"

import type { NavItem } from "@/components/shared/AppLayout"

export const mainNavItems: NavItem[] = [
  {
    title: "Dashboard",
    url: "/dashboard",
    icon: IconDashboard,
  },
  {
    title: "Brand Deep Dive",
    url: "/brand-deep-dive",
    icon: IconListDetails,
  },
  {
    title: "Marketing Dashboard",
    url: "/marketing-dashboard",
    icon: IconChartBar,
  },
  {
    title: "Executive Summary",
    url: "/executive-summary",
    icon: IconReport,
  },
  {
    title: "Budget",
    url: "/budget",
    icon: IconWallet,
  },
  {
    title: "AI Assistant",
    url: "/ai-assistant",
    icon: IconFileAi,
  },
]

export const mainSecondaryNavItems: NavItem[] = [
  {
    title: "Settings",
    url: "#",
    icon: IconSettings,
  },
  {
    title: "Get Help",
    url: "#",
    icon: IconHelp,
  },
  {
    title: "Search",
    url: "#",
    icon: IconSearch,
  },
]
