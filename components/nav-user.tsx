"use client"

import {
  Ava<PERSON>,
  AvatarFallback,
  AvatarImage,
} from "./ui/avatar"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu"
import {
  IconCreditCard,
  IconDotsVertical,
  IconLayoutDashboard,
  IconLogin,
  IconLogout,
  IconNotification,
  IconUserCircle,
} from "@tabler/icons-react"
import {
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  useSidebar,
} from "./ui/sidebar"
import { signIn, signOut, useSession } from "next-auth/react"

import { Button } from "./ui/button"
import { IconUserX } from "@tabler/icons-react"
import Link from "next/link"
import { Skeleton } from "./ui/skeleton" // Assuming you have a Skeleton component
import { toast } from "sonner"

export function NavUser() {
  const { data: session, status, update: updateSession } = useSession()
  const { isMobile } = useSidebar()

  const handleStopImpersonation = async () => {
    try {
      const response = await fetch('/api/admin/impersonation/stop', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to stop impersonation');
      }

      await updateSession({ revertImpersonation: true });
      
      toast.success('Impersonation stopped successfully.');
      
    } catch (error) {
      console.error('Stop impersonation error:', error);
      toast.error(error instanceof Error ? error.message : 'Could not stop impersonation.');
    }
  };

  if (status === "loading") {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <SidebarMenuButton
            size="lg"
            className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
          >
            <Skeleton className="h-8 w-8 rounded-lg" />
            <div className="grid flex-1 text-left text-sm leading-tight">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="mt-1 h-3 w-32" />
            </div>
            <IconDotsVertical className="ml-auto size-4" />
          </SidebarMenuButton>
        </SidebarMenuItem>
      </SidebarMenu>
    )
  }

  if (status === "unauthenticated") {
    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <Button
            variant="outline"
            className="w-full justify-start"
            onClick={() => signIn("google")}
          >
            <IconLogin className="mr-2 size-4" />
            Sign in with Google
          </Button>
        </SidebarMenuItem>
      </SidebarMenu>
    )
  }

  if (status === "authenticated" && session?.user) {
    const user = session.user
    // Explicitly type session.user to include isSuperAdmin and impersonation properties
    const typedUser = user as {
      name?: string | null;
      email?: string | null;
      image?: string | null;
      isSuperAdmin?: boolean;
      isImpersonating?: boolean;
      originalUser?: { name?: string | null; email?: string | null; };
    };

    return (
      <SidebarMenu>
        <SidebarMenuItem>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <SidebarMenuButton
                size="lg"
                className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              >
                <Avatar className="h-8 w-8 rounded-lg grayscale">
                  <AvatarImage src={typedUser.image ?? undefined} alt={typedUser.name ?? "User"} />
                  <AvatarFallback className="rounded-lg">
                    {typedUser.name?.charAt(0).toUpperCase() || typedUser.email?.charAt(0).toUpperCase() || "U"}
                  </AvatarFallback>
                </Avatar>
                <div className="grid flex-1 text-left text-sm leading-tight">
                  <span className="truncate font-medium">{typedUser.name || typedUser.email}</span>
                  <span className="text-muted-foreground truncate text-xs">
                    {typedUser.email}
                  </span>
                </div>
                <IconDotsVertical className="ml-auto size-4" />
              </SidebarMenuButton>
            </DropdownMenuTrigger>
            <DropdownMenuContent
              className="w-(--radix-dropdown-menu-trigger-width) min-w-56 rounded-lg"
              side={isMobile ? "bottom" : "right"}
              align="end"
              sideOffset={4}
            >
              <DropdownMenuLabel className="p-0 font-normal">
                <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                  <Avatar className="h-8 w-8 rounded-lg">
                    <AvatarImage src={typedUser.image ?? undefined} alt={typedUser.name ?? "User"} />
                    <AvatarFallback className="rounded-lg">
                      {typedUser.name?.charAt(0).toUpperCase() || typedUser.email?.charAt(0).toUpperCase() || "U"}
                    </AvatarFallback>
                  </Avatar>
                  <div className="grid flex-1 text-left text-sm leading-tight">
                    <span className="truncate font-medium">{typedUser.name || typedUser.email}</span>
                    <span className="text-muted-foreground truncate text-xs">
                      {typedUser.email}
                    </span>
                    {typedUser.isImpersonating && (
                      <span className="text-orange-600 truncate text-xs font-medium">
                        Impersonating (was: {typedUser.originalUser?.name || typedUser.originalUser?.email})
                      </span>
                    )}
                  </div>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuGroup>
                {typedUser.isImpersonating && (
                  <DropdownMenuItem onClick={handleStopImpersonation} className="text-orange-600 focus:text-orange-700">
                    <IconUserX className="mr-2 size-4" />
                    Stop Impersonation
                  </DropdownMenuItem>
                )}
                {(() => {
                  // Check if user has admin access via roles or isSuperAdmin flag
                  const userRoles = (session.user as { roles?: string[] })?.roles || [];
                  const hasAdminAccess = userRoles.includes('Admin') || userRoles.includes('Super Admin') || typedUser.isSuperAdmin;
                  
                  return hasAdminAccess && (
                    <Link href="/admin">
                      <DropdownMenuItem>
                        <IconLayoutDashboard className="mr-2 size-4" />
                        Admin Dashboard
                      </DropdownMenuItem>
                    </Link>
                  );
                })()}
                <DropdownMenuItem>
                  <IconUserCircle className="mr-2 size-4" />
                  Account
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <IconCreditCard className="mr-2 size-4" />
                  Billing
                </DropdownMenuItem>
                <DropdownMenuItem>
                  <IconNotification className="mr-2 size-4" />
                  Notifications
                </DropdownMenuItem>
              </DropdownMenuGroup>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={() => signOut()}>
                <IconLogout className="mr-2 size-4" />
                Log out
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </SidebarMenuItem>
      </SidebarMenu>
    )
  }

  return null // Should not happen if status is handled correctly
}
